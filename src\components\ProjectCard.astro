---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
  image?: string;
}

const { title, description, tags, slug, image } = Astro.props;

// Truncate long technology names for better display
const formatTag = (tag: string) => {
  if (tag.length > 12) {
    return tag.substring(0, 10) + '..';
  }
  return tag;
};

// Limit to maximum 6 tags and ensure we show only 2 rows
const displayTags = tags.slice(0, 6);
---

<article class="portfolio-card group card-interactive relative overflow-hidden min-h-[400px] flex flex-col" role="listitem">
  <!-- Sophisticated gradient border accent -->
  <div class="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-primary-600 via-primary-500 to-accent-600 rounded-t-3xl"></div>

  <!-- Optional project image placeholder -->
  {image && (
    <div class="relative h-48 bg-gradient-to-br from-primary-50/80 to-accent-50/60 dark:from-primary-950/30 dark:to-accent-950/20 overflow-hidden">
      <img
        src={image}
        alt={`${title} preview`}
        class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
        loading="lazy"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-primary-900/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    </div>
  )}

  <!-- Card content with sophisticated spacing -->
  <div class="portfolio-content p-8 flex flex-col flex-grow">
    <!-- Title section - enhanced typography -->
    <div class="mb-5 h-16 flex items-start">
      <h3 class="text-xl font-bold text-text-light dark:text-text-dark group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-all duration-500 line-clamp-2 tracking-tight">
        {title}
      </h3>
    </div>

    <!-- Description section - improved readability -->
    <div class="mb-8 flex-grow">
      <p class="text-text-light-muted dark:text-text-dark-muted leading-relaxed text-base line-clamp-3">
        {description}
      </p>
    </div>

    <!-- Technology tags section - modern 2025 styling -->
    <div class="mt-auto">
      <div class="flex flex-wrap gap-2.5 min-h-[4.5rem] overflow-hidden">
        {displayTags.map((tag, index) => (
          <span
            class="tag inline-flex items-center px-4 py-2 bg-gradient-to-r from-white/90 to-secondary-50/80 dark:from-secondary-800/80 dark:to-secondary-700/60 text-primary-700 dark:text-primary-300 text-sm font-medium rounded-full border border-primary-200/40 dark:border-primary-600/30 backdrop-blur-sm hover:from-primary-50 hover:to-primary-100/70 dark:hover:from-primary-900/40 dark:hover:to-primary-800/50 hover:border-primary-400/60 dark:hover:border-primary-500/50 transition-all duration-400 hover:scale-105 hover:-translate-y-1 shadow-sm hover:shadow-lg group-hover:shadow-md"
            style={`animation-delay: ${index * 0.08}s;`}
          >
            {formatTag(tag)}
          </span>
        ))}
      </div>
    </div>
  </div>

  <!-- Enhanced hover overlay effect -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-600/3 via-transparent to-accent-600/2 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-3xl"></div>
  
  <!-- Subtle glow effect on hover -->
  <div class="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" style="box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 0 20px rgba(158, 122, 104, 0.1);"></div>
</article>

<style>
  /* Line clamping utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Modern 2025 sophisticated hover effects */
  .portfolio-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 
      0 20px 64px -12px rgba(158, 122, 104, 0.25),
      0 8px 32px -8px rgba(45, 42, 35, 0.1),
      0 0 0 1px rgba(158, 122, 104, 0.1);
  }

  .portfolio-card:hover .tag {
    animation: sophisticatedTagFloat 0.6s ease-out forwards;
  }

  @keyframes sophisticatedTagFloat {
    0% {
      transform: translateY(0) scale(1);
      opacity: 0.9;
    }
    30% {
      transform: translateY(-3px) scale(1.05);
      opacity: 1;
    }
    60% {
      transform: translateY(-1px) scale(1.02);
      opacity: 1;
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  /* Enhanced card dimensions and appearance */
  .portfolio-card {
    height: 400px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Sophisticated dark mode adjustments */
  .dark .portfolio-card {
    background: rgba(36, 33, 24, 0.6);
    border-color: rgba(232, 230, 225, 0.08);
  }

  .dark .portfolio-card:hover {
    background: rgba(36, 33, 24, 0.8);
    border-color: rgba(158, 122, 104, 0.3);
    box-shadow: 
      0 20px 64px -12px rgba(0, 0, 0, 0.4),
      0 8px 32px -8px rgba(158, 122, 104, 0.15),
      0 0 0 1px rgba(158, 122, 104, 0.2);
  }

  /* Smooth transitions for all elements */
  .portfolio-card,
  .portfolio-card * {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced responsiveness */
  @media (max-width: 768px) {
    .portfolio-card {
      height: auto;
      min-height: 360px;
    }
    
    .portfolio-card:hover {
      transform: translateY(-6px) scale(1.01);
    }
  }
</style>