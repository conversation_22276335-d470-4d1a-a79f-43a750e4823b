---
import Layout from '../layouts/Layout.astro';
import ThemeToggle from '../components/ThemeToggle.astro';
---

<Layout title="Modern Theme Toggle Test">
  <div class="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
    <div class="container mx-auto px-4 py-16">
      <div class="max-w-2xl mx-auto text-center">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-8">
          Modern Theme Toggle Test
        </h1>
        
        <p class="text-lg text-gray-600 dark:text-gray-300 mb-12">
          Testing the new 2025 glassmorphism toggle switch design with smooth animations
        </p>
        
        <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
            Fixed Theme Toggle Switch
          </h2>
          
          <div class="flex justify-center">
            <ThemeToggle />
          </div>
          
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-4">
            Fixed: Simple toggle switch with smooth sliding animation
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-300 mb-3">
              Light Mode Features
            </h3>
            <ul class="text-sm text-blue-800 dark:text-blue-400 space-y-2">
              <li>• Bright, clean interface</li>
              <li>• Optimal for daytime use</li>
              <li>• Better contrast for text</li>
              <li>• Professional appearance</li>
            </ul>
          </div>
          
          <div class="bg-indigo-50 dark:bg-indigo-900/20 rounded-xl p-6">
            <h3 class="text-lg font-semibold text-indigo-900 dark:text-indigo-300 mb-3">
              Dark Mode Features
            </h3>
            <ul class="text-sm text-indigo-800 dark:text-indigo-400 space-y-2">
              <li>• Reduced eye strain</li>
              <li>• Better for low-light</li>
              <li>• Battery saving (OLED)</li>
              <li>• Modern aesthetic</li>
            </ul>
          </div>
        </div>
        
        <div class="mt-12 p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl">
          <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Toggle Switch Features
          </h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="w-8 h-8 mx-auto mb-2 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                ✨
              </div>
              <p class="text-gray-600 dark:text-gray-400">Glassmorphism</p>
            </div>
            <div class="text-center">
              <div class="w-8 h-8 mx-auto mb-2 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                🎯
              </div>
              <p class="text-gray-600 dark:text-gray-400">Smooth Slide</p>
            </div>
            <div class="text-center">
              <div class="w-8 h-8 mx-auto mb-2 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
                📱
              </div>
              <p class="text-gray-600 dark:text-gray-400">Mobile Ready</p>
            </div>
            <div class="text-center">
              <div class="w-8 h-8 mx-auto mb-2 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                ♿
              </div>
              <p class="text-gray-600 dark:text-gray-400">Accessible</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>