---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Theme Animation Demo | Nob Hokleng">
  <!-- Hero Section with Animation Demo -->
  <section class="hero relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background-light via-background-light-secondary to-primary-50/30 dark:from-background-dark dark:via-background-dark-secondary dark:to-primary-950/30">
    
    <!-- Animated background elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-br from-primary-400/10 to-accent-400/10 rounded-full blur-3xl animate-float"></div>
      <div class="absolute bottom-1/3 right-1/4 w-96 h-96 bg-gradient-to-br from-accent-400/10 to-primary-400/10 rounded-full blur-3xl animate-float" style="animation-delay: 2s;"></div>
    </div>

    <div class="container-custom relative z-10 py-20">
      <div class="text-center space-y-8">
        
        <!-- Main heading -->
        <div class="animate-on-scroll">
          <h1 class="text-5xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
            Theme Animation Demo
          </h1>
          <p class="text-xl lg:text-2xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            Experience modern theme switching with advanced page-wide animations
          </p>
        </div>

        <!-- Animation Features Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16 animate-on-scroll">
          
          <!-- Curtain Transition -->
          <div class="card p-6 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 theme-transition-stagger">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-width="2" d="M8 3H5a2 2 0 00-2 2v3m2-5h10m-5 0v18m0-18a2 2 0 012 2v3M3 16v3a2 2 0 002 2h3m10-5v3a2 2 0 01-2 2h-3"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-secondary-900 dark:text-secondary-100">Curtain Transition</h3>
            <p class="text-secondary-600 dark:text-secondary-400">Elegant dual-panel slide effect for professional portfolios (Desktop)</p>
          </div>

          <!-- Fade Blur Transition -->
          <div class="card p-6 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 theme-transition-stagger">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-secondary-900 dark:text-secondary-100">Fade Blur Effect</h3>
            <p class="text-secondary-600 dark:text-secondary-400">Smooth fade with subtle blur for tablet devices</p>
          </div>

          <!-- Staggered Animation -->
          <div class="card p-6 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 theme-transition-stagger">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-secondary-900 dark:text-secondary-100">Staggered Elements</h3>
            <p class="text-secondary-600 dark:text-secondary-400">Sequential animation of page elements with timing delays</p>
          </div>

          <!-- Icon Morphing -->
          <div class="card p-6 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 theme-transition-stagger">
            <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="4" stroke-width="2"/>
                <path stroke-width="2" d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 6.34L4.93 4.93M19.07 19.07l-1.41-1.41"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-secondary-900 dark:text-secondary-100">Icon Morphing</h3>
            <p class="text-secondary-600 dark:text-secondary-400">Smooth sun-to-moon transformation with spring physics</p>
          </div>

          <!-- Ripple Effect -->
          <div class="card p-6 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 theme-transition-stagger">
            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="3" stroke-width="2"/>
                <circle cx="12" cy="12" r="8" stroke-width="2" opacity="0.5"/>
                <circle cx="12" cy="12" r="13" stroke-width="2" opacity="0.25"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-secondary-900 dark:text-secondary-100">Ripple Feedback</h3>
            <p class="text-secondary-600 dark:text-secondary-400">Visual feedback with expanding ripple from click position</p>
          </div>

          <!-- Accessibility -->
          <div class="card p-6 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 theme-transition-stagger">
            <div class="w-12 h-12 bg-gradient-to-br from-rose-500 to-rose-600 rounded-xl flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-secondary-900 dark:text-secondary-100">Accessibility First</h3>
            <p class="text-secondary-600 dark:text-secondary-400">Respects reduced motion preferences and screen readers</p>
          </div>

        </div>

        <!-- Instructions -->
        <div class="mt-16 p-8 bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-2xl animate-on-scroll">
          <h2 class="text-2xl font-bold mb-4 text-secondary-900 dark:text-secondary-100">Try It Out!</h2>
          <p class="text-lg text-secondary-700 dark:text-secondary-300 mb-4">
            Click the theme toggle button in the header to experience the page-wide animation effects.
          </p>
          <div class="flex flex-wrap gap-4 justify-center">
            <span class="px-4 py-2 bg-white/50 dark:bg-secondary-800/50 rounded-lg text-sm font-medium">
              🖥️ Desktop: Curtain Transition
            </span>
            <span class="px-4 py-2 bg-white/50 dark:bg-secondary-800/50 rounded-lg text-sm font-medium">
              📱 Mobile: Slide Up Effect
            </span>
            <span class="px-4 py-2 bg-white/50 dark:bg-secondary-800/50 rounded-lg text-sm font-medium">
              📱 Tablet: Fade Blur Effect
            </span>
            <span class="px-4 py-2 bg-white/50 dark:bg-secondary-800/50 rounded-lg text-sm font-medium">
              ♿ Reduced Motion: Simple Fade
            </span>
          </div>
        </div>

      </div>
    </div>
  </section>

  <!-- Technical Details Section -->
  <section class="py-24 bg-secondary-50 dark:bg-secondary-900/50">
    <div class="container-custom">
      <div class="text-center mb-16 animate-on-scroll">
        <h2 class="text-4xl font-bold mb-6 text-secondary-900 dark:text-secondary-100">Technical Implementation</h2>
        <p class="text-xl text-secondary-600 dark:text-secondary-400 max-w-3xl mx-auto">
          Built with modern web technologies and performance optimization in mind
        </p>
      </div>

      <div class="grid lg:grid-cols-2 gap-12 animate-on-scroll">
        
        <!-- Performance Features -->
        <div class="space-y-6">
          <h3 class="text-2xl font-semibold text-secondary-900 dark:text-secondary-100">Performance Features</h3>
          <ul class="space-y-4">
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">GPU-accelerated animations using transform and opacity</span>
            </li>
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">Automatic mobile optimization with lighter effects</span>
            </li>
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">Efficient memory management and cleanup</span>
            </li>
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">Intersection Observer for optimized scroll animations</span>
            </li>
          </ul>
        </div>

        <!-- Accessibility Features -->
        <div class="space-y-6">
          <h3 class="text-2xl font-semibold text-secondary-900 dark:text-secondary-100">Accessibility Features</h3>
          <ul class="space-y-4">
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">Respects prefers-reduced-motion settings</span>
            </li>
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">Screen reader announcements for theme changes</span>
            </li>
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">Keyboard navigation support</span>
            </li>
            <li class="flex items-start space-x-3">
              <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
              <span class="text-secondary-700 dark:text-secondary-300">WCAG 2.1 AA compliant color contrasts</span>
            </li>
          </ul>
        </div>

      </div>
    </div>
  </section>
</Layout>

<style>
  /* Demo-specific styles */
  .card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .card:hover {
    transform: translateY(-4px);
  }
  
  /* Enhanced floating animation for demo */
  @keyframes demo-float {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg); 
    }
    33% { 
      transform: translateY(-8px) rotate(1deg); 
    }
    66% { 
      transform: translateY(-4px) rotate(-0.5deg); 
    }
  }
  
  .animate-float {
    animation: demo-float 4s ease-in-out infinite;
  }
</style>
