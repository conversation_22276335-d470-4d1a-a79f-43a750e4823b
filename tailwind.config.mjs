/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Professional 2025 color palette - sophisticated neutrals with warm accents
        primary: {
          50: '#f7f6f4',
          100: '#efede8',
          200: '#ddd8ce',
          300: '#c5bead',
          400: '#b1a18c',
          500: '#9e7a68', // Pantone Mocha Mousse 2025
          600: '#8a6b59',
          700: '#72584b',
          800: '#5e4a40',
          900: '#4d3e37',
          950: '#28201c',
        },
        secondary: {
          50: '#f9f9f8',
          100: '#f3f3f1',
          200: '#e5e4e1',
          300: '#d1cfc9',
          400: '#b8b5ac',
          500: '#a19e92',
          600: '#8a877c',
          700: '#726f65',
          800: '#5e5c54',
          900: '#4e4c46',
          950: '#282723',
        },
        accent: {
          50: '#fefcf3',
          100: '#fef8e6',
          200: '#fcf0c7',
          300: '#f9e29e',
          400: '#f5d173',
          500: '#f1bf4f',
          600: '#e6a635',
          700: '#c08729',
          800: '#9a6a25',
          900: '#7d5621',
          950: '#432c10',
        },
        // Semantic colors
        success: {
          50: '#f0fdf4',
          500: '#22c55e',
          600: '#16a34a',
        },
        warning: {
          50: '#fffbeb',
          500: '#f59e0b',
          600: '#d97706',
        },
        error: {
          50: '#fef2f2',
          500: '#ef4444',
          600: '#dc2626',
        },
        // Background colors - warm neutrals for professional appeal
        background: {
          light: '#fefefe',
          'light-secondary': '#faf9f7',
          'light-soft': '#f5f4f1',
          dark: '#1a1916',
          'dark-secondary': '#242118',
          'dark-soft': '#2d2a23',
        },
        // Text colors - improved contrast with warm undertones
        text: {
          light: '#2d2a23',
          'light-secondary': '#4e4c46',
          'light-muted': '#726f65',
          dark: '#fefefe',
          'dark-secondary': '#e8e6e1',
          'dark-muted': '#b8b5ac',
        },
      },
      fontFamily: {
        heading: ['Inter', 'system-ui', 'sans-serif'],
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      spacing: {
        '15': '3.75rem',
        '18': '4.5rem',
        '88': '22rem',
        '100': '25rem',
        '112': '28rem',
        '128': '32rem',
      },
      borderRadius: {
        '4xl': '2rem',
        '5xl': '2.5rem',
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'large': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        'glow': '0 0 20px rgba(59, 130, 246, 0.15)',
        'glow-accent': '0 0 20px rgba(249, 115, 22, 0.15)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
        'bounce-subtle': 'bounceSubtle 2s infinite',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
      backdropBlur: {
        'xs': '2px',
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            '--tw-prose-body': theme('colors.secondary[600]'),
            '--tw-prose-headings': theme('colors.secondary[800]'),
            '--tw-prose-lead': theme('colors.secondary[600]'),
            '--tw-prose-links': theme('colors.primary[600]'),
            '--tw-prose-bold': theme('colors.secondary[800]'),
            '--tw-prose-counters': theme('colors.secondary[500]'),
            '--tw-prose-bullets': theme('colors.secondary[300]'),
            '--tw-prose-hr': theme('colors.secondary[200]'),
            '--tw-prose-quotes': theme('colors.secondary[800]'),
            '--tw-prose-quote-borders': theme('colors.secondary[200]'),
            '--tw-prose-captions': theme('colors.secondary[500]'),
            '--tw-prose-code': theme('colors.primary[600]'),
            '--tw-prose-pre-code': theme('colors.secondary[200]'),
            '--tw-prose-pre-bg': theme('colors.secondary[100]'),
            '--tw-prose-th-borders': theme('colors.secondary[300]'),
            '--tw-prose-td-borders': theme('colors.secondary[200]'),
            '--tw-prose-invert-body': theme('colors.secondary[400]'),
            '--tw-prose-invert-headings': theme('colors.secondary[200]'),
            '--tw-prose-invert-lead': theme('colors.secondary[400]'),
            '--tw-prose-invert-links': theme('colors.primary[400]'),
            '--tw-prose-invert-bold': theme('colors.secondary[200]'),
            '--tw-prose-invert-counters': theme('colors.secondary[400]'),
            '--tw-prose-invert-bullets': theme('colors.secondary[600]'),
            '--tw-prose-invert-hr': theme('colors.secondary[700]'),
            '--tw-prose-invert-quotes': theme('colors.secondary[200]'),
            '--tw-prose-invert-quote-borders': theme('colors.secondary[700]'),
            '--tw-prose-invert-captions': theme('colors.secondary[400]'),
            '--tw-prose-invert-code': theme('colors.primary[400]'),
            '--tw-prose-invert-pre-code': theme('colors.secondary[300]'),
            '--tw-prose-invert-pre-bg': theme('colors.secondary[800]'),
            '--tw-prose-invert-th-borders': theme('colors.secondary[600]'),
            '--tw-prose-invert-td-borders': theme('colors.secondary[700]'),
            maxWidth: 'none',
            fontSize: theme('fontSize.base[0]'),
            lineHeight: theme('fontSize.base[1].lineHeight'),
            p: {
              marginTop: theme('spacing.4'),
              marginBottom: theme('spacing.4'),
            },
            h1: {
              fontSize: theme('fontSize.3xl[0]'),
              lineHeight: theme('fontSize.3xl[1].lineHeight'),
              marginTop: theme('spacing.8'),
              marginBottom: theme('spacing.6'),
              fontWeight: theme('fontWeight.bold'),
            },
            h2: {
              fontSize: theme('fontSize.2xl[0]'),
              lineHeight: theme('fontSize.2xl[1].lineHeight'),
              marginTop: theme('spacing.8'),
              marginBottom: theme('spacing.4'),
              fontWeight: theme('fontWeight.semibold'),
            },
            h3: {
              fontSize: theme('fontSize.xl[0]'),
              lineHeight: theme('fontSize.xl[1].lineHeight'),
              marginTop: theme('spacing.6'),
              marginBottom: theme('spacing.3'),
              fontWeight: theme('fontWeight.semibold'),
            },
            h4: {
              fontSize: theme('fontSize.lg[0]'),
              lineHeight: theme('fontSize.lg[1].lineHeight'),
              marginTop: theme('spacing.6'),
              marginBottom: theme('spacing.3'),
              fontWeight: theme('fontWeight.medium'),
            },
            ul: {
              marginTop: theme('spacing.4'),
              marginBottom: theme('spacing.4'),
              paddingLeft: theme('spacing.6'),
            },
            ol: {
              marginTop: theme('spacing.4'),
              marginBottom: theme('spacing.4'),
              paddingLeft: theme('spacing.6'),
            },
            li: {
              marginTop: theme('spacing.1'),
              marginBottom: theme('spacing.1'),
            },
            blockquote: {
              marginTop: theme('spacing.6'),
              marginBottom: theme('spacing.6'),
              paddingLeft: theme('spacing.6'),
              borderLeftWidth: '4px',
              borderLeftColor: theme('colors.primary[500]'),
              fontStyle: 'italic',
            },
            code: {
              backgroundColor: theme('colors.secondary[100]'),
              paddingTop: theme('spacing.1'),
              paddingBottom: theme('spacing.1'),
              paddingLeft: theme('spacing.1'),
              paddingRight: theme('spacing.1'),
              borderRadius: theme('borderRadius.md'),
              fontSize: theme('fontSize.sm[0]'),
              fontWeight: theme('fontWeight.medium'),
            },
            'code::before': {
              content: '""',
            },
            'code::after': {
              content: '""',
            },
            pre: {
              backgroundColor: theme('colors.secondary[100]'),
              color: theme('colors.secondary[800]'),
              borderRadius: theme('borderRadius.lg'),
              padding: theme('spacing.4'),
              marginTop: theme('spacing.6'),
              marginBottom: theme('spacing.6'),
              fontSize: theme('fontSize.sm[0]'),
              lineHeight: theme('fontSize.sm[1].lineHeight'),
            },
            'pre code': {
              backgroundColor: 'transparent',
              padding: '0',
              borderRadius: '0',
              fontSize: 'inherit',
              fontWeight: 'inherit',
            },
          },
        },
      }),
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
};