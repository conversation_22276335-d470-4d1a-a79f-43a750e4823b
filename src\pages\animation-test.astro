---
import Layout from '../layouts/Layout.astro';
import ThemeToggle from '../components/ThemeToggle.astro';
---

<Layout title="Theme Toggle Animation Test">
  <div class="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
    <div class="container mx-auto px-4 py-16">
      <div class="max-w-lg mx-auto text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Animation Test
        </h1>
        
        <div class="bg-gray-100 dark:bg-gray-800 rounded-xl p-8 mb-8">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-6">
            Toggle Switch Animations
          </h2>
          
          <div class="flex justify-center mb-6">
            <ThemeToggle />
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
            <p>✓ Thumb should slide smoothly left/right</p>
            <p>✓ Icons should fade in/out with rotation</p>
            <p>✓ Background should change color</p>
            <p>✓ All animations should be 300ms</p>
          </div>
        </div>
        
        <div class="space-y-4 text-left">
          <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 class="font-semibold text-blue-900 dark:text-blue-300 mb-2">Expected Behavior:</h3>
            <ul class="text-sm text-blue-800 dark:text-blue-400 space-y-1">
              <li>• Light mode: thumb on left, sun icon visible</li>
              <li>• Dark mode: thumb on right, moon icon visible</li>
              <li>• Smooth sliding animation when clicked</li>
              <li>• Icons rotate and fade during transition</li>
            </ul>
          </div>
        </div>
        
        <button 
          onclick="testAnimation()" 
          class="mt-6 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Test Animation Programmatically
        </button>
      </div>
    </div>
  </div>
  
  <script>
    function testAnimation() {
      const toggle = document.getElementById('theme-toggle');
      if (toggle) {
        toggle.click();
        console.log('Theme toggle clicked programmatically');
        console.log('Current data-theme:', toggle.getAttribute('data-theme'));
        console.log('Current aria-checked:', toggle.getAttribute('aria-checked'));
      }
    }
    
    // Add animation event listeners for debugging
    document.addEventListener('DOMContentLoaded', () => {
      const thumb = document.querySelector('.toggle-thumb');
      const icons = document.querySelectorAll('.icon');
      
      if (thumb) {
        thumb.addEventListener('transitionend', (e) => {
          console.log('Thumb animation completed:', e.propertyName);
        });
      }
      
      icons.forEach(icon => {
        icon.addEventListener('transitionend', (e) => {
          console.log('Icon animation completed:', e.propertyName, icon.classList.contains('sun-icon') ? 'sun' : 'moon');
        });
      });
    });
  </script>
</Layout>