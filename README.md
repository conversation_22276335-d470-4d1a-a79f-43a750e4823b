# NobiSite - Professional Portfolio Website

[![Astro](https://img.shields.io/badge/Astro-4.0+-FF5D01?style=flat&logo=astro&logoColor=white)](https://astro.build)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8+-3178C6?style=flat&logo=typescript&logoColor=white)](https://www.typescriptlang.org)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4+-06B6D4?style=flat&logo=tailwindcss&logoColor=white)](https://tailwindcss.com)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

> 🚀 **Modern Portfolio Website** - Built with Astro.js, TypeScript, and Tailwind CSS

## About

A modern, high-performance portfolio website built with cutting-edge web technologies. This project showcases professional development practices, modern architecture patterns, and performance-first design principles.

**Use Case:** Professional portfolio for software engineers, developers, and tech professionals seeking to showcase their work and attract opportunities.

## 🚀 Quick Demo

Visit the live site: [**nobi-site.vercel.app**](https://nobi-site.vercel.app) *(Replace with your actual URL)*

### Key Highlights
- ⚡ **Lightning Fast** - Astro's zero-JS approach for optimal performance
- 🎨 **Modern Design** - Clean, professional UI with Tailwind CSS
- 📱 **Fully Responsive** - Perfect on desktop, tablet, and mobile
- ♿ **Accessible** - Built with accessibility best practices
- 🔍 **SEO Optimized** - Structured data and meta tags included

## Project Status

**Current Phase:** ✅ **Production Ready** - Fully implemented and deployed
**Development Status:** Active development with continuous improvements

### Implementation Status
- [x] **Planning & Documentation** - Comprehensive project documentation
- [x] **Technology Stack Selection** - Modern stack implemented
- [x] **Development** - Astro.js + TypeScript + Tailwind CSS implementation
- [x] **Core Features** - Portfolio, about, contact, and resume pages
- [x] **Content Management** - MDX-based content system
- [ ] **Enhancement & Optimization** - Performance and SEO improvements
- [ ] **Testing & Deployment** - Quality assurance and production launch

### Quality Targets
- **Performance:** 95+ Lighthouse score, <1.2s load time
- **Accessibility:** WCAG 2.1 AA compliance
- **SEO:** Comprehensive optimization with structured data
- **Code Quality:** TypeScript, modern development practices

## Features & Pages

### Implemented Pages
- **🏠 Homepage** (`/`) - Hero section with professional introduction and call-to-action
- **👤 About** (`/about`) - Professional background, skills, and experience
- **💼 Portfolio** (`/portfolio`) - Project showcase with detailed case studies
- **📄 Resume** (`/resume`) - Interactive resume with downloadable PDF
- **📞 Contact** (`/contact`) - Professional contact form and social links
- **📚 Resources** (`/resources`) - Curated bookmarks and technical resources

### Technical Features
- **⚡ Performance Optimized** - Astro's zero-JS by default approach
- **🔍 SEO Ready** - Automatic sitemap generation and meta tag management
- **♿ Accessibility Compliant** - WCAG 2.1 AA standards with semantic HTML
- **📱 Mobile-First Design** - Responsive design across all devices
- **🎨 Modern UI/UX** - Clean, professional design with Tailwind CSS
- **📝 Content Management** - MDX-based content system for easy updates
- **🚀 Fast Loading** - Optimized images, minimal JavaScript, efficient CSS

### Development Features
- **🔧 TypeScript** - Full type safety and better developer experience
- **🎯 Component Architecture** - Reusable Astro components
- **🎨 Design System** - Consistent styling with Tailwind CSS
- **📦 Modern Tooling** - Vite, pnpm, and modern build pipeline
- **🔄 Hot Reload** - Fast development with instant updates

## Technology Stack

### Current Implementation
- **Frontend Framework:** Astro.js 4.0+ with TypeScript
- **CSS Framework:** Tailwind CSS 3.4+ with custom design system
- **Content Management:** MDX with Frontmatter for dynamic content
- **Build Tools:** Vite (integrated with Astro)
- **Package Manager:** pnpm for efficient dependency management
- **Development:** TypeScript for type safety and better DX

### Deployment & Hosting
- **Hosting Platform:** Vercel (Primary) + Netlify (Backup)
- **Analytics:** Vercel Analytics + Google Analytics 4
- **Domain:** Custom domain with automatic HTTPS
- **CI/CD:** Automatic deployment from GitHub

### Technology Selection Rationale

| Aspect | Astro.js Choice | Benefits |
|--------|----------------|----------|
| **Performance** | Zero-JS by default | 95+ Lighthouse scores, <1.2s load time |
| **SEO** | Built-in SSG | Automatic sitemap, meta tags, structured data |
| **Developer Experience** | Modern tooling | Hot reload, TypeScript, component architecture |
| **Maintainability** | Component-based | Reusable components, utility-first CSS |
| **Learning Curve** | Minimal | Familiar HTML/CSS/JS syntax |
| **Portfolio Fit** | Perfect match | Designed for content-focused sites |

## Getting Started

### Prerequisites
- Node.js 18+
- pnpm (recommended) or npm

### Development Setup
```bash
# Clone the repository
git clone https://github.com/Nobhokleng/nobi-site.git
cd nobi-site

# Install dependencies
pnpm install

# Start development server
pnpm dev

# Open browser to http://localhost:4321
```

### Build & Deployment
```bash
# Build for production
pnpm build

# Preview production build locally
pnpm preview

# Type checking
pnpm check
```

### Production Deployment
- **Platform:** Vercel with automatic GitHub integration
- **Features:** Custom domain, automatic HTTPS, performance monitoring
- **Build Command:** `pnpm build`
- **Output Directory:** `dist/`

## 📚 Documentation

Project documentation is organized in the [`docs/`](./docs/) folder:

| Document | Description |
|----------|-------------|
| [🏗️ Technical Architecture](./docs/technical-architecture.md) | Complete technology stack, architecture decisions, and implementation details |
| [🎨 Design Guidelines](./docs/design_guidelines.md) | Visual design, branding, and Tailwind configuration |
| [📝 Content Plan](./docs/content_plan.md) | Content strategy and creation guidelines |

### 🎯 TaskMaster Project Management
Task management and enhancement roadmap available in [`.taskmaster/`](./.taskmaster/):
- **[📋 PRD](/.taskmaster/docs/prd.txt)** - Product Requirements Document with enhancement roadmap
- **[🎯 Tasks](/.taskmaster/tasks/tasks.json)** - 15 structured development tasks
- **[📊 Summary](/.taskmaster/docs/implementation-summary.md)** - TaskMaster implementation overview

## Project Structure

```
nobi-site/
├── src/
│   ├── components/          # Reusable Astro components
│   ├── content/            # MDX content files
│   ├── layouts/            # Page layouts
│   ├── pages/              # Route pages
│   ├── styles/             # Global styles
│   └── utils/              # Utility functions
├── docs/                   # Project documentation
├── dist/                   # Built output (generated)
└── public/                 # Static assets
```

## Contributing

This project serves as a portfolio template and demonstration of modern web development practices. Feel free to use it as inspiration for your own portfolio projects.

### Development Guidelines
- Follow TypeScript best practices
- Use Tailwind CSS for styling
- Maintain component modularity
- Write semantic HTML
- Ensure accessibility compliance

## License

This project is open source and available under the [MIT License](LICENSE).

---

**Built with ❤️ using Astro.js, TypeScript, and Tailwind CSS**
