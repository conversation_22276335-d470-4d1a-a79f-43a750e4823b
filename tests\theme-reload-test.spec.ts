import { test, expect } from '@playwright/test';

test.describe('Theme Toggle Reload Test', () => {
  test('should persist theme between normal reload and hard reload', async ({ page }) => {
    // Navigate to the homepage
    await page.goto('http://localhost:4322');
    
    // Wait for page to load and theme to initialize
    await page.waitForLoadState('networkidle');
    
    // Step 1: Check current theme state
    const initialState = await page.evaluate(() => {
      return {
        htmlClasses: document.documentElement.className,
        localStorage: localStorage.getItem('theme'),
        initialTheme: (window as any).__INITIAL_THEME__,
        buttonExists: !!document.getElementById('theme-toggle'),
        buttonDataTheme: document.getElementById('theme-toggle')?.getAttribute('data-theme'),
        buttonAriaChecked: document.getElementById('theme-toggle')?.getAttribute('aria-checked')
      };
    });
    
    console.log('Initial State:', initialState);
    
    // Step 2: Toggle theme to dark using JavaScript (since clicking is problematic)
    await page.evaluate(() => {
      const button = document.getElementById('theme-toggle');
      if (button) {
        button.click();
      }
    });
    
    // Wait for theme change to complete
    await page.waitForTimeout(500);
    
    const afterToggleState = await page.evaluate(() => {
      return {
        htmlClasses: document.documentElement.className,
        localStorage: localStorage.getItem('theme'),
        buttonDataTheme: document.getElementById('theme-toggle')?.getAttribute('data-theme'),
        buttonAriaChecked: document.getElementById('theme-toggle')?.getAttribute('aria-checked')
      };
    });
    
    console.log('After Toggle State:', afterToggleState);
    
    // Step 3: Perform normal reload
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const afterNormalReloadState = await page.evaluate(() => {
      return {
        htmlClasses: document.documentElement.className,
        localStorage: localStorage.getItem('theme'),
        initialTheme: (window as any).__INITIAL_THEME__,
        buttonDataTheme: document.getElementById('theme-toggle')?.getAttribute('data-theme'),
        buttonAriaChecked: document.getElementById('theme-toggle')?.getAttribute('aria-checked')
      };
    });
    
    console.log('After Normal Reload State:', afterNormalReloadState);
    
    // Step 4: Check if theme persisted after normal reload
    expect(afterNormalReloadState.localStorage).toBe('dark');
    expect(afterNormalReloadState.htmlClasses).toContain('dark');
    
    // Step 5: Perform hard reload (simulate by clearing cache and reloading)
    await page.evaluate(() => {
      // Clear service worker cache to simulate hard reload
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => registration.unregister());
        });
      }
    });
    
    // Clear browser cache and reload
    await page.reload({ waitUntil: 'networkidle' });
    
    const afterHardReloadState = await page.evaluate(() => {
      return {
        htmlClasses: document.documentElement.className,
        localStorage: localStorage.getItem('theme'),
        initialTheme: (window as any).__INITIAL_THEME__,
        buttonDataTheme: document.getElementById('theme-toggle')?.getAttribute('data-theme'),
        buttonAriaChecked: document.getElementById('theme-toggle')?.getAttribute('aria-checked')
      };
    });
    
    console.log('After Hard Reload State:', afterHardReloadState);
    
    // Step 6: Compare results - theme should persist in both cases
    expect(afterHardReloadState.localStorage).toBe('dark');
    expect(afterHardReloadState.htmlClasses).toContain('dark');
    expect(afterNormalReloadState.localStorage).toBe(afterHardReloadState.localStorage);
    expect(afterNormalReloadState.htmlClasses).toBe(afterHardReloadState.htmlClasses);
  });
  
  test('should debug theme initialization timing', async ({ page }) => {
    // Listen to console messages to track theme initialization
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      if (msg.text().includes('Theme Init')) {
        consoleMessages.push(msg.text());
      }
    });
    
    await page.goto('http://localhost:4322');
    await page.waitForLoadState('networkidle');
    
    // Check how many times theme was initialized
    console.log('Theme initialization messages:', consoleMessages);
    
    // There should only be one theme initialization per page load
    expect(consoleMessages.length).toBeLessThanOrEqual(2); // Allow for one potential duplicate
  });
});
