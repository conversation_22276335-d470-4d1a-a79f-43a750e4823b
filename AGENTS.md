# AGENTS.md - Development Guidelines

## Build/Test Commands
- `npm run dev` - Start development server at localhost:4321
- `npm run build` - Build for production
- `npm run preview` - Preview built site
- `npm run check` - TypeScript checking for Astro components
- `npm run perf:test` - Run performance tests
- `npx playwright test` - Run all Playwright tests
- `npx playwright test tests/theme-reload-test.spec.ts` - Run single test file
- `npx playwright test --headed` - Run tests with browser UI visible

## Code Style Guidelines
- **Framework**: Astro.js with TypeScript, Tailwind CSS
- **Imports**: Use relative imports for components, absolute for utils with `@/*` alias
- **Components**: Astro components (.astro) with frontmatter, HTML, script, style sections
- **Types**: Strict TypeScript with Zod schemas for content collections
- **Naming**: camelCase for variables/functions, PascalCase for components, kebab-case for files
- **Content**: MDX files with frontmatter validation via Zod schemas
- **Styling**: Tailwind classes, custom CSS variables for themes, glass-card utility classes
- **Scripts**: TypeScript in Astro script tags, proper DOM ready checks, event cleanup
- **Error Handling**: Null checks, optional chaining, graceful degradation
- **Accessibility**: ARIA labels, semantic HTML, keyboard navigation support
- **Performance**: Image optimization with Sharp, CSS code splitting, chunk optimization